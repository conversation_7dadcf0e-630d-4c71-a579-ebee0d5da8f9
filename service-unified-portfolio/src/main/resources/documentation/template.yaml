openapi: 3.0.0
info:
  title: Unified Portfolio Service
  version: 1.0.0
x-basepath: /simon/api/v1/unified-portfolio
x-kong-service-defaults:
  read_timeout: 300000
tags:
  - name: service-unified-portfolio
    description: Unified Portfolio Service
paths:
  /info:
    get:
      x-scopes:
        - admin
        - simon-system-user
      summary: Retrieve service info
      responses:
        200:
          description: Success
  /healthcheck:
    get:
      x-scopes:
        - admin
        - simon-system-user
        - fa-manager
      summary: Retrieve service healthcheck
      responses:
        200:
          description: Success
  /uptime:
    get:
      x-scopes:
        - admin
        - simon-system-user
        - fa-manager
      summary: Retrieve service uptime
      responses:
        200:
          description: Success
  /owners/{id}:
    get:
      x-scopes:
        - admin
        - simon-system-user
        - fa-manager
      summary: Retrieve Owner by id
      parameters:
        - name: id
          in: path
          required: true
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.EntityID}
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.unifiedportfolio.domain.view.OwnerView}
    patch:
      x-scopes:
        - admin
        - simon-system-user
        - fa-manager
      summary: patch update owner fields
      parameters:
        - name: id
          in: path
          required: true
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.EntityID}
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
      responses:
        200:
          description: owner updated
  /holdings/{id}:
    get:
      x-scopes:
        - admin
        - simon-system-user
        - fa-manager
      summary: Retrieve Owner by id
      parameters:
        - name: id
          in: path
          required: true
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.EntityID}
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.unifiedportfolio.domain.view.OwnerView}

  /presentation/regbi/households:
    get:
      x-scopes:
        - admin
        - fa-manager
        - fa
        - wholesaler
        - issuer
      parameters:
        - name: sortBy
          description: sortBy column
          in: query
          required: false
          schema:
            $ref: ${com.simonmarkets.regbi.domain.HouseholdSortBy.Ref}
        - name: sortOrder
          description: sort order
          in: query
          required: false
          schema:
            $ref: ${com.simonmarkets.regbi.domain.SortOrder.Ref}
        - name: filterByConcentration
          description: filters by si concentration
          in: query
          explode: true
          style: form
          required: false
          schema:
            type: array
            items:
              type: string
        - name: filterByLNW
          description: filters by liquid net worth
          in: query
          explode: true
          style: form
          required: false
          schema:
            type: array
            items:
              type: string
        - name: pattern
          description: fuzzy search pattern to search by name
          in: query
          required: false
          schema:
            $ref: ${com.simonmarkets.regbi.domain.OpenApiDefinitions.SearchPattern}
        - name: offset
          description: Skip first n items
          in: query
          required: false
          schema:
            $ref: ${com.simonmarkets.regbi.domain.OpenApiDefinitions.Offset}
        - name: limit
          description: Maximum number of results to return
          in: query
          required: false
          schema:
            $ref: ${com.simonmarkets.regbi.domain.OpenApiDefinitions.Limit}
      x-kong-name: regbi_household_list
      description: Get regBi household list
      responses:
        200:
          description: Households retrieved successfully
          content:
            application/json:
              schema:
                type: object

  /presentation/regbi/households/{id}/issuer-concentration:
    get:
      x-scopes:
        - admin
        - fa-manager
        - fa
        - wholesaler
        - issuer
      parameters:
        - name: id
          in: path
          required: true
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.Symbol}
      x-kong-name: regbi_household_issuer_concentration
      description: Get household issuer concentration
      responses:
        200:
          description: Issuer concentration retrieved successfully
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.regbi.api.response.SiIssuerConcentrationResponse}

  /presentation/snowflake-test:
    get:
      x-scopes:
        - admin
        - fa-manager
        - fa
        - wholesaler
        - issuer
      x-kong-name: snowflake_test_connection
      description: get data from test snowflake
      responses:
        200:
          description: test data received

  /integrations/addepar/owners:
    get:
      x-scopes:
        - admin
        - simon-system-user
        - fa
        - fa-manager
      summary: Get Addepar owners
      parameters:
        - name: offset
          in: query
          required: false
          schema:
            $ref: ${com.simonmarkets.regbi.domain.OpenApiDefinitions.Offset}
        - name: limit
          in: query
          required: false
          schema:
            $ref: ${com.simonmarkets.regbi.domain.OpenApiDefinitions.Limit}
    responses:
      200:
        description: Success
        content:
          application/json:
            schema:
              $ref: ${com.simonmarkets.unifiedportfolio.domain.view.OwnershipView}

  /integrations/addepar/owners/{ownerId}/holdings:
    get:
      x-scopes:
        - admin
        - simon-system-user
        - fa
        - fa-manager
      summary: Get Addepar holdings for Owner
    responses:
      200:
        description: Success
        content:
          application/json:
            schema:
              $ref: ${com.simonmarkets.unifiedportfolio.domain.view.ShallowHoldingView}
