package com.simonmarkets.regbi.service

import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.capabilities.{UnifiedHoldingsCapabilities, UnifiedOwnersCapabilities}
import com.simonmarkets.clients.ContractOfferingsClient
import com.simonmarkets.clients.ContractOfferingsClient.Offering
import com.simonmarkets.http.HttpError
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.regbi.api.request.GetHouseholdListRequest
import com.simonmarkets.regbi.api.response.{DecryptedAccount, DecryptedProfile, GetHouseholdListSummaryResult, SiIssuerConcentrationResponse}
import com.simonmarkets.regbi.domain.{HouseholdSortBy, Page, SortOrder, TestSnowflakeUser}
import com.simonmarkets.regbi.domain.SortOrder.Asc
import com.simonmarkets.regbi.repository.{HoldingPresentationRepository, OwnersPresentationRepository}
import com.simonmarkets.regbi.repository.OwnersPresentationRepositoryMongo.{DecryptedShallowHousehold, HouseholdSummary, ShallowHousehold, ShallowHouseholdDetails}
import com.simonmarkets.unifiedportfolio.domain.data.HouseholdDetails
import com.simonmarkets.utils.IcnEncryptionHelper
import io.simon.encryption.v2.model.EncryptedData
import io.simon.encryption.v3.service.IcnEncryptionService
import io.simon.encryption.v3.conversion.DefaultConverters.implicitStringReader

import scala.util.matching.Regex
import scala.concurrent.{ExecutionContext, Future}

trait RegBiPresentationService {
  def getHouseholdList(request: GetHouseholdListRequest)(implicit traceId: TraceId, user: UserACL): Future[Page[GetHouseholdListSummaryResult]]

  def getHouseholdSiIssuerConcentration(householdId: String)(implicit traceId: TraceId, user: UserACL): Future[List[SiIssuerConcentrationResponse]]

  def getTestSnowflakeData(implicit traceId: TraceId, user: UserACL): Future[List[TestSnowflakeUser]]
}

class RegBiPresentationServiceImpl(
    ownersRepo: OwnersPresentationRepository,
    holdingsRepo: HoldingPresentationRepository,
    override val encryptionService: IcnEncryptionService,
    contractOfferingsClient: ContractOfferingsClient,
    snowflakeProvider: SnowflakeProvider
)(implicit val executionContext: ExecutionContext) extends RegBiPresentationService with IcnEncryptionHelper with TraceLogging {

  override def getTestSnowflakeData(implicit traceId: TraceId, user: UserACL): Future[List[TestSnowflakeUser]] = {
    snowflakeProvider.getTestData
  }

  override def getHouseholdList(request: GetHouseholdListRequest)(implicit traceId: TraceId, user: UserACL): Future[Page[GetHouseholdListSummaryResult]] = {
    log.info(s"Getting household list for the request: $request")
    val availableAccessKeys = UnifiedOwnersCapabilities.getAvailableAccessKeysForCapabilities(UnifiedOwnersCapabilities.ViewCapabilities, user)
    val sortOrder = request.sortOrder.getOrElse(Asc)
    val sortBy = request.sortBy.getOrElse(HouseholdSortBy.Id)
    for {
      households <- if (request.pattern.nonEmpty) ownersRepo.getHouseholdNames(availableAccessKeys) else Future(Seq.empty)
      decryptedHouseholds <- getDecryptedShallowHouseholds(households)
      ids = decryptedHouseholds.filter(household => matchesPattern(household.name, request.pattern.getOrElse(""))).map(_.id)
      res <- ownersRepo.getHouseholdList(
        ids = ids,
        availableAccessKeys = availableAccessKeys,
        sortOrder = sortOrder,
        sortBy = sortBy,
        filterByLNW = request.filterByLNW,
        filterByConcentration = request.filterByConcentration,
        offset = request.offset,
        limit = request.limit
      )
      totalCount = res.flatMap(_.totalCount).headOption.fold(0)(_.numberOfItems.toInt)
      results <- getDecryptedHouseholdSummaries(res.flatMap(_.results))
      sorted = request.sortBy match {
        case Some(HouseholdSortBy.Name) if sortOrder == SortOrder.Asc => results.sortBy(_.name)
        case Some(HouseholdSortBy.Name) if sortOrder == SortOrder.Desc => results.sortBy(_.name)(Ordering[String].reverse)
        case _ => results
      }
    } yield Page(totalCount = totalCount, result = sorted)
  }

  override def getHouseholdSiIssuerConcentration(householdId: String)(implicit traceId: TraceId, user: UserACL): Future[List[SiIssuerConcentrationResponse]] = {
    val householdAccessKeys = UnifiedOwnersCapabilities.getAvailableAccessKeysForCapabilities(UnifiedOwnersCapabilities.ViewCapabilities, user)
    val holdingAccessKeys = UnifiedHoldingsCapabilities.getAvailableAccessKeysForCapabilities(UnifiedHoldingsCapabilities.ViewCapabilities, user)
    for {
      household <- findHouseholdById(householdId, householdAccessKeys)
      holdings <- holdingsRepo.getHoldingsByHousehold(householdId, holdingAccessKeys)
      amountMap = holdings.flatMap { h => h.productId.cusip.map(cusip => cusip -> h.totalCommitment.amount)}.toMap
      holdingIds = holdings.map(_.productId).flatMap(_.cusip).distinct
      groupedOfferings <- groupedOfferingsByName(holdingIds)
      lnw = getHouseholdLnw(household.details)
      res = groupedOfferings.map { case (issuerShortName, issuerList) =>
        val issuerTotalAmount = issuerList.flatMap(offering => amountMap.get(offering.id)).sum
        val issuerConcentration = issuerTotalAmount.toFloat / lnw * 100
        SiIssuerConcentrationResponse(issuer = issuerShortName, concentration = issuerConcentration, amount = issuerTotalAmount)
      }.toList
    } yield res
  }

  private def groupedOfferingsByName(holdingIds: Seq[String])(implicit traceId: TraceId): Future[Map[String, List[Offering]]] = {
    contractOfferingsClient.getIssuerNames(holdingIds).map(result => result.results.groupBy(_.params.issuerShortName))
  }

  private def getHouseholdLnw(householdDetails: HouseholdDetails): Int = {
    // TODO: take liquid net worth from accounts
    householdDetails.liquidNetWorth.map(_.amount).getOrElse(householdDetails.liquidNetWorthMin.map(_.amount).getOrElse(1))
  }
  private def findHouseholdById(householdId: String, availableAccessKeys: Set[String])(implicit traceId: TraceId): Future[ShallowHouseholdDetails] = {
    log.info("Getting household:", householdId)

    ownersRepo.getHouseholdById(householdId, availableAccessKeys).flatMap {
      case Some(household) => Future.successful(household)
      case None => Future.failed(HttpError.notFound("Household was not found"))
    }
  }

  private def getDecryptedShallowHouseholds(households: Seq[ShallowHousehold])(implicit traceId: TraceId): Future[List[DecryptedShallowHousehold]] = {
    val zippedShallowHouseholds = households.zipWithIndex
    val householdPlainDataMap = zippedShallowHouseholds.flatMap { case (household, index) => Seq(constructKey("name", index) -> Some(household.name)) }.toMap
    for {
      decryptMap <- if (householdPlainDataMap.isEmpty) Future.successful(Map.empty[String, String]) else getDecryptedDataOpt[String, String](householdPlainDataMap)
      decryptedHouseholds = zippedShallowHouseholds.flatMap { case (input, index) => decryptMap.get(constructKey("name", index)).map(name => DecryptedShallowHousehold(id = input.id, name = name))}.toList
    } yield decryptedHouseholds
  }

  private def constructHouseholdSummaryDataMap(zippedHouseholdSummaries: Seq[(HouseholdSummary, Int)]): Map[String, Some[EncryptedData]] = {
    zippedHouseholdSummaries.flatMap {
      case (household, householdIndex) =>

        val householdKeys = Seq(constructKey("name", householdIndex) -> Some(household.name))
        val profilesKeys = household.profiles.zipWithIndex.flatMap {
          case (profile, profileIndex) =>
            Seq(constructKey(s"profileName:$householdIndex", profileIndex) -> Some(profile.name))
        }
        val accountKeys = household.profiles.zipWithIndex.flatMap {
          case (profile, profileIndex) =>
            profile.accounts.zipWithIndex.map {
              case (account, accountIndex) =>
                constructKey(s"accountName:$householdIndex:$profileIndex", accountIndex) -> Some(account.name)
            }
        }

        householdKeys ++ profilesKeys ++ accountKeys
    }.toMap
  }

  private def getDecryptedHouseholdSummaries(summaries: Seq[HouseholdSummary])(implicit traceId: TraceId): Future[List[GetHouseholdListSummaryResult]] = {
    val zippedHouseholdSummaries = summaries.zipWithIndex
    val summaryPlainDataMap = constructHouseholdSummaryDataMap(zippedHouseholdSummaries)
    for {
      decryptedDataMap <- getDecryptedDataOpt(summaryPlainDataMap)
      decryptedSummaryResult = zippedHouseholdSummaries.map {
        case (household, householdIndex) =>
          val name = decryptedDataMap.get(constructKey("name", householdIndex))

          val decryptedProfiles = household.profiles.zipWithIndex.map {
            case (profile, profileIndex) =>
              val decryptedProfileName = decryptedDataMap.get(constructKey(s"profileName:$householdIndex", profileIndex))

              val decryptedAccounts = profile.accounts.zipWithIndex.map {
                case (account, accountIndex) =>
                  val decryptedAccountName = decryptedDataMap.get(constructKey(s"accountName:$householdIndex:$profileIndex", accountIndex))
                  DecryptedAccount(
                    id = account.id,
                    name = decryptedAccountName.getOrElse(""),
                    investmentObjective = account.investmentObjective,
                    documents = account.documents,
                    totalValue = account.totalValue,
                    partnerExtensionsLPL = account.partnerExtensionsLPL,
                    liquidNetWorthMin = account.liquidNetWorthMin,
                    liquidNetWorthMax = account.liquidNetWorthMax
                  )
              }
              DecryptedProfile(
                id = profile.id,
                experience = profile.experience,
                age = profile.age,
                name = decryptedProfileName.getOrElse(""),
                productTypes = profile.productTypes,
                accounts = decryptedAccounts
              )
          }
          GetHouseholdListSummaryResult(
            id = household.id,
            name = name.getOrElse(""),
            lnw = household.lnw,
            siConcentrationPercentage = household.siConcentrationPercentage,
            annualExpenses = household.annualExpenses,
            availableLCF = household.availableLCF,
            obligations = household.obligations,
            profiles = decryptedProfiles
          )
      }.toList
    } yield decryptedSummaryResult
  }

  private def matchesPattern(value: String, pattern: String): Boolean = {
    val regex = (".*(?i)" + Regex.quote(pattern) + ".*").r
    regex.findFirstIn(value).isDefined
  }
}
