package com.simonmarkets.unifiedportfolio.utils

import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.logging.TraceId
import com.simonmarkets.unifiedportfolio.access.{HoldingKeysGenerator, OwnerKeysGenerator}
import com.simonmarkets.unifiedportfolio.domain.data._
import com.simonmarkets.unifiedportfolio.domain.enums.lpl.{LPLAccountPrimaryClassification, LPLAccountRType, LPLAccountSecondaryClassification}
import com.simonmarkets.unifiedportfolio.domain.enums.{Currency, ExperienceProductType, HoldingType, LegalEntityType, OwnerType}
import io.simon.encryption.v2.model.EncryptedData
import simon.Id.NetworkId

import java.time.LocalDateTime

import scala.util.Random

// This is intended to be used whenever example data is desired. E.g.:
//  * getting an environment up and running with a specific amount of data, perhaps for load testing
//  * Unit test factories
trait ExampleDataFactory {

  val networkId = NetworkId("SIMON Admin") // todo make LPL

  private val rand = new Random()

  // TODO is this an aspect of the data or should it be moved elsewhere?
  implicit val traceId: TraceId = TraceId.randomize
  implicit val userACL: UserACL = UserACL(
    userId = "testUser",
    networkId = networkId,
    lastVisitedAt = Some(LocalDateTime.now),
    email = "<EMAIL>",
    firstName = "firstName",
    lastName = "lastName",
    distributorId = None,
    omsId = None,
    tradewebEligible = false,
    regSEligible = false,
    isActive = Some(true),
    capabilities = Set("viewUnifiedOwnerViaNetwork", "editUnifiedOwnerViaNetwork", "viewUnifiedHoldingViaNetwork")
  )


  // todo must reference pre-existing, persistent entities like locations
  def fakeAuthorization() = {
    Authorization(
      Set.empty, // TODO entitlements
      Set.empty, // TODO advisorIds
      Set.empty, // todo faNumbers
      Set.empty, // todo locations
      networkId,
    )
  }: Authorization

  def exampleString(fieldName: String, index: Int) = {
    s"${fieldName}_${index}"
  }

  def exampleTimestamps() = {
    RecordTimestamps(LocalDateTime.now(), LocalDateTime.now())
  }: RecordTimestamps

  def exampleHouseholdDetail(i: Int) = {
    // TODO make money more random / configurable
    val minValue = i * 10000
    HouseholdDetails(
      liquidNetWorthMin = Some(Money(Currency.USD, minValue)),
      liquidNetWorthMax = Some(Money(Currency.USD, minValue * 2)),
      annualExpenses = Some(Money(Currency.USD, minValue * 2)),
      availableLCF = Some(Money(Currency.USD, minValue * 2)),
      obligations = Some(Money(Currency.USD, minValue * 2))
    )
  }: HouseholdDetails


  def exampleHousehold(i: Int = 1): Owner = {
    val o = Owner(
      exampleString("household", i),
      Set.empty,
      OwnerType.Household,
      EncryptedData(exampleString("household", i), "test", Array[scala.Byte](), None),
      Set.empty, // todo add external ids
      Set.empty, // todo add parent ids
      Set.empty, // todo add child ids
      fakeAuthorization(),
      exampleTimestamps(),
      exampleHouseholdDetail(i),
    )
    o.copy(acceptedAccessKeys = OwnerKeysGenerator.getAcceptedAccessKeys(o))
  }

  def examplePerson(household: Owner = exampleHousehold(), i: Int = 1): Owner = {
    val o = Owner(
      exampleString(s"${household.id}_person", i),
      Set.empty,
      OwnerType.LegalEntity,
      //      encrypt("John Doe"), // todo get random
      EncryptedData("John Doe", "test", Array[scala.Byte](), None),
      Set.empty, // todo add external ids
      Set(household.id),
      Set.empty, // todo add child ids
      fakeAuthorization(),
      exampleTimestamps(),
      examplePersonDetails(i),
    )
    o.copy(acceptedAccessKeys = OwnerKeysGenerator.getAcceptedAccessKeys(o))
  }

  def examplePersonEntity(): Person = {
    Person(
      // todo make fields vary more.
      experience = Some(InvestorExperience("SI", 20)),
      age = Some(50),
      productTypes = Set(ProductType(ExperienceProductType.`Structured/Market-Linked Growth Notes`, 3), ProductType(ExperienceProductType.`Structured/Market-Linked Principal At Risk Notes With American Barrier or Sector Underlier`, 5))
    )
  }

  def examplePersonDetails(i: Int = 1): LegalEntityDetails = {
    LegalEntityDetails(
      // todo make all more random and/or configurable
      //      Some(encrypt(exampleString("SSN", i))),
      Some(EncryptedData(exampleString("SSN", i), "test", Array[scala.Byte](), None)),
      LegalEntityType.Person,
      examplePersonEntity(),
    )
  }

  def exampleAccountDetails(i: Int = 1): AccountDetails = {
    AccountDetails(
      // todo make all more random and/or configurable
      Some("Retirement"),
      //      Some(encrypt(s"000-111-222-${i}")),
      Some(EncryptedData(s"000-111-222-${i}", "test", Array[scala.Byte](), None)),
      Some("growth"),
      Some(exampleMoney),
      Set.empty,
      Some(LPLAccountExtensions(LPLAccountPrimaryClassification.SWM, LPLAccountSecondaryClassification.AS2, LPLAccountRType.FS)),
      None,
      None
    )
  }

  def exampleAccount(person: Owner = examplePerson(), i: Int = 1): Owner = {
    val prefix = s"${person.id}_account"
    val o = Owner(
      exampleString(prefix, i),
      Set.empty,
      OwnerType.Account,
      //      encrypt(exampleString(prefix, i)),
      EncryptedData(exampleString(prefix, i), "test", Array[scala.Byte](), None),
      Set.empty, // todo add external ids
      Set(person.id),
      Set.empty, // todo add child ids
      fakeAuthorization(),
      exampleTimestamps(),
      exampleAccountDetails(i),
    )
    o.copy(acceptedAccessKeys = OwnerKeysGenerator.getAcceptedAccessKeys(o))
  }

  def exampleProductId(i: Int = 1): ProductId =
    ProductId(
      Some(if (i % 2 == 0) "TWD0708AC" else "40057F3S1"),
      Some(exampleString("isin", i)),
      Some(exampleString("icap", i)),
      Set(ExternalId(exampleString("lpl", i), "lpl", "LPL", false)),
    )

  // todo add controllable params
  def exampleMoney(): Money =
    Money(Currency.USD, rand.nextInt(200) + 300)

  def exampleSiHolding(account: Owner = exampleAccount(), i: Int, ancestorIds: Set[String]): Holding = {
    // todo provide default ancestorIds based on input account. not clear how best to do that
    val h = Holding(
      exampleString(s"${account.id}_holding", i),
      Set.empty,
      HoldingType.StructuredInvestment,
      exampleProductId(i),
      Some(exampleMoney()),
      Some(exampleMoney()),
      Some(exampleMoney()),
      Set(Ownership(account.id, account.name, account.ownerType, Some(1.0))),
      ancestorIds,
      fakeAuthorization(),
      exampleTimestamps(),
      StructuredInvestmentDetails()
    )
    h.copy(acceptedAccessKeys = HoldingKeysGenerator.getAcceptedAccessKeys(h))
  }

  def exampleAltsHolding(account: Owner = exampleAccount(), i: Int, ancestorIds: Set[String]): Holding = {
    // todo provide default ancestorIds based on input account. not clear how best to do that
    val h = Holding(
      exampleString(s"${account.id}_holding", i),
      Set.empty,
      HoldingType.AlternativeInvestment,
      exampleProductId(i),
      Some(Money(Currency.USD, 500)), // TODO make more random
      Some(Money(Currency.USD, 400)), // TODO make more random
      Some(Money(Currency.USD, 300)),
      Set(Ownership(account.id, account.name, account.ownerType, Some(1.0))),
      ancestorIds,
      fakeAuthorization(),
      exampleTimestamps(),
      AlternativeDetails(exampleString("shareClass", i)),
    )
    h.copy(acceptedAccessKeys = HoldingKeysGenerator.getAcceptedAccessKeys(h))
  }

  def exampleTraditionalHolding(account: Owner = exampleAccount(), i: Int, ancestorIds: Set[String]): Holding = {
    // todo provide default ancestorIds based on input account. not clear how best to do that
    val h = Holding(
      exampleString(s"${account.id}_holding", i),
      Set.empty,
      HoldingType.TraditionalInvestment,
      exampleProductId(i),
      Some(exampleMoney()),
      Some(exampleMoney()),
      Some(exampleMoney()),
      Set(Ownership(account.id, account.name, account.ownerType, Some(1.0))),
      ancestorIds,
      fakeAuthorization(),
      exampleTimestamps(),
      TraditionalDetails(exampleString("TICKER", i)),
    )
    h.copy(acceptedAccessKeys = HoldingKeysGenerator.getAcceptedAccessKeys(h))
  }

  def exampleAnnuitiesHolding(account: Owner = exampleAccount(), i: Int, ancestorIds: Set[String]): Holding = {
    // todo provide default ancestorIds based on input account. not clear how best to do that
    val h = Holding(
      exampleString(s"${account.id}_holding", i),
      Set.empty,
      HoldingType.Annuity,
      exampleProductId(i),
      Some(exampleMoney()),
      Some(exampleMoney()),
      Some(exampleMoney()),
      Set(Ownership(account.id, account.name, account.ownerType, Some(1.0))),
      ancestorIds,
      fakeAuthorization(),
      exampleTimestamps(),
      AnnuityDetails(
        totalContractAmount = Some(rand.nextInt(10000)),
        surrenderValue = Some(rand.nextInt(10000)),
        totalPremium = Some(rand.nextInt(10000)),
        policyNumber = exampleString("PolicyNumber", i),
        productName = Some(exampleString("Product Name", i)),
        carrierKey = Some(exampleString("CARRIERKEY", i)),
        annuityAssetClass = exampleString("Annuity Asset Class", i)
      ),
    )
    h.copy(acceptedAccessKeys = HoldingKeysGenerator.getAcceptedAccessKeys(h))
  }



}
