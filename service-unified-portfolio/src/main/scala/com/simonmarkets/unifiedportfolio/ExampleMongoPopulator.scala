package com.simonmarkets.unifiedportfolio

import akka.actor.ActorSystem
import akka.http.scaladsl.Http
import akka.stream.Materializer
import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.api.users.HttpACLClient
import com.simonmarkets.http.FutureHttpClient
import com.simonmarkets.mongodb.Client
import com.simonmarkets.resteasy.authn.User
import com.simonmarkets.resteasy.framework.RestEasyCore
import com.simonmarkets.resteasy.requests.AuthorizedDirectives
import com.simonmarkets.syntax.futureOpsConversion
import com.simonmarkets.unifiedportfolio.config.AppConfiguration
import com.simonmarkets.unifiedportfolio.domain.data.{AccountDetails, LegalEntityDetails, Owner}
import com.simonmarkets.unifiedportfolio.repository.HoldingRepository.HoldingRepositoryMongo
import com.simonmarkets.unifiedportfolio.repository.OwnerRepository.OwnerRepositoryMongo
import com.simonmarkets.unifiedportfolio.utils.ExampleDataFactory
import com.simonmarkets.useracl.UserAclAuthorizedDirective
import com.simonmarkets.utils.IcnEncryptionHelper
import com.simonmarkets.utils.config.Resolvers.SecretsConfigOps
import com.simonmarkets.utils.data.mongo.context.DatabaseContext
import com.typesafe.config.ConfigFactory
import io.simon.encryption.v3.conversion.DefaultConverters.{implicitStringSearch, implicitStringWriter}
import io.simon.encryption.v3.service.IcnEncryptionService
import pureconfig.loadConfigOrThrow
import pureconfig.generic.auto._


import scala.annotation.tailrec
import scala.concurrent.ExecutionContext
import scala.concurrent.duration.Duration

object ExampleMongoPopulator extends App with IcnEncryptionHelper with ExampleDataFactory {

  private val rawConfig = ConfigFactory.load().resolveSecrets()
  val config: AppConfiguration = loadConfigOrThrow[AppConfiguration](rawConfig)

  implicit lazy val actorSystem: ActorSystem = ActorSystem("ExampleMongoPopulator")
  implicit lazy val mat: Materializer = Materializer(actorSystem)
  implicit lazy val executionContext: ExecutionContext = actorSystem.dispatcher

  private val httpAclClient = HttpACLClient(config.aclClientConfig)
  private val systemUser = httpAclClient.getUserACL(config.systemUserId).await(Duration.Inf)

  // this line to inspect a user other than the system user.  It's Matthew Forsyth
   private val myUser = httpAclClient.getUserACL("a0a1ef5e-90a5-4bfa-aede-b2f1a1978a24").await(Duration.Inf)

  private val userAclDirective = UserAclAuthorizedDirective(httpAclClient)
  val authDirective: AuthorizedDirectives[UserACL] = RestEasyCore.allowSystemCalls[UserACL](userAclDirective, User(systemUser.userId, "", None, Nil), systemUser)
  val futureHttpClient = new FutureHttpClient(Http(), config.aclClientConfig.httpClient)
  override val encryptionService: IcnEncryptionService = new IcnEncryptionService(futureHttpClient, config.icnEncryption)

  private val mongoClient = Client.create(config.mongoDB.client)
  private val mongoDatabase = mongoClient.getDatabase(config.mongoDB.owner.database)
  private implicit val databaseContext: DatabaseContext = DatabaseContext(mongoDatabase, mongoClient)
  val ownerRepository: OwnerRepositoryMongo = new OwnerRepositoryMongo()
  val holdingRepository: HoldingRepositoryMongo = new HoldingRepositoryMongo()

  println("Example Mongo Populator launching!")

  def ancestorIds(owner: Owner): Set[String] = {
    @tailrec def recursiveAncestorIds(acc: Set[String], newAdditions: Set[Owner]): Set[String] = {
      val newParentIds = newAdditions.flatMap(owner => owner.parentIds)
      // TODO there will be some equivalent of this next line in the production code, where data needs
      // to be loaded from Mongo. It will talk to Mongo instead of just traversing the in-memory Owners.
      val newParents = allOwners.filter(owner => newParentIds.contains(owner.id))
      if (newParents.isEmpty) return acc
      recursiveAncestorIds(acc ++ newParentIds, newParents.toSet)
    }

    recursiveAncestorIds(Set(owner.id), Set(owner))
  }

  // todo make all configurable and/or randomizable
  val NumHouseholds = 10
  val NumPeoplePerHousehold = 2
  val NumAccountsPerPerson = 6
  val NumHoldingsPerAccount = 10

  val households = (1 to NumHouseholds).map { i => exampleHousehold(i) }

  val zippedHouseholds = households.zipWithIndex.toMap
  val householdsPlainDataMap = zippedHouseholds.flatMap {
    case (input, index) => Seq(constructKey("name", index) -> Some(input.name.key))
  }

  val encryptedHouseholdDataMap = getEncryptedDataOpt(householdsPlainDataMap, myUser.networkId).await(Duration.Inf)
  val encryptedHouseholds = zippedHouseholds.flatMap {
    case (input, index) => encryptedHouseholdDataMap.get(constructKey("name", index)).map( name => input.copy(name = name))
  }.toList

  val people = households.flatMap { household =>
    (1 to NumPeoplePerHousehold).map { i => examplePerson(household, i) }
  }

  val zippedPeople = people.zipWithIndex.toMap
  val peoplePlainDataMap = zippedPeople.flatMap {
    case (input, index) => Seq(
      constructKey("name", index) -> Some(input.name.key),
      constructKey("taxId", index) -> (input.details match {
        case entity: LegalEntityDetails => entity.taxId.map(_.key)
        case _ => None
      })
    )
  }

  val encryptedPeopleDataMap = getEncryptedDataOpt(peoplePlainDataMap, myUser.networkId).await(Duration.Inf)
  val encryptedPeople = zippedPeople.map {
    case (input, index) =>
      val name = encryptedPeopleDataMap.get(constructKey("name", index))
      val taxId = encryptedPeopleDataMap.get(constructKey("taxId", index))
      input match {
        case owner@Owner(_, _, _, _, _, _, _, _, _, details: LegalEntityDetails) => owner.copy(name = name.get, details = details.copy(taxId = taxId))
        case _ => input.copy(name = name.get)
      }
  }

  val accounts = people.flatMap { person =>
    (1 to NumAccountsPerPerson).map { i => exampleAccount(person, i) }
  }

  val zippedAccounts = accounts.zipWithIndex.toMap
  val accountsPlainData = zippedAccounts.flatMap {
    case (input, index) => Seq(
      constructKey("name", index) -> Some(input.name.key),
      constructKey("accountNumber", index) -> (input.details match {
        case entity: AccountDetails => entity.accountNumber.map(_.key)
        case _ => None
      })
    )
  }

  val encryptedAccountDataMap = getEncryptedDataOpt(accountsPlainData, myUser.networkId).await(Duration.Inf)
  val encryptedAccounts = zippedAccounts.map {
    case (input, index) =>
      val name = encryptedAccountDataMap.get(constructKey("name", index))
      val accountNumber = encryptedAccountDataMap.get(constructKey("accountNumber", index))
      input match {
        case owner@Owner(_, _, _, _, _, _, _, _, _, details: AccountDetails) => owner.copy(name = name.get, details = details.copy(accountNumber = accountNumber))
        case _ => input.copy(name = name.get)
      }
  }

  val allOwners = encryptedHouseholds ++ encryptedPeople ++ encryptedAccounts

  val holdings = accounts.flatMap { account =>
    (1 to NumHoldingsPerAccount).map { i => exampleSiHolding(account, i, ancestorIds(account)) }
  }.toList

//  val ownerEditKeys = UnifiedOwnersCapabilities.getAvailableAccessKeysForCapabilities(UnifiedOwnersCapabilities.EditCapabilities, systemUser)
  println("dropping owners collection...")
  ownerRepository.clearAllData.await(Duration.Inf)
  println("adding all owners...")
  val ownerResults = ownerRepository.seedRecords(allOwners).await(Duration.Inf)

  println("dropping holdings collection...")
  holdingRepository.clearAllData.await(Duration.Inf)
  println("Adding holdings...")
  val holdingResults = holdingRepository.seedRecords(holdings).await(Duration.Inf)

  actorSystem.terminate() // TODO this doesn't work if an exception happens before we get here.  Catch exceptions.
}