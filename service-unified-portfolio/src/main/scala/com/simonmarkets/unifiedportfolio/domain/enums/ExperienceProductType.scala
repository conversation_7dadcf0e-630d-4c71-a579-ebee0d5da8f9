package com.simonmarkets.unifiedportfolio.domain.enums

import com.simonmarkets.util.{EnumEntry, ProductEnums}
import io.simon.openapi.annotation.Field.EnumValues
import io.simon.openapi.annotation.Reference

sealed trait ExperienceProductType extends EnumEntry

object ExperienceProductType extends ProductEnums[ExperienceProductType] {

  case object `Structured/Market-Linked CDs` extends ExperienceProductType
  case object `Structured/Market-Linked Principal Protected Notes` extends ExperienceProductType
  case object `Structured/Market-Linked Principal At Risk Notes With European Barrier` extends ExperienceProductType
  case object `Structured/Market-Linked Principal At Risk Notes With American Barrier or Sector Underlier` extends ExperienceProductType
  case object `Structured/Market-Linked Growth Notes` extends ExperienceProductType
  case object `Structured/Market-Linked Income Notes` extends ExperienceProductType

  override def Values: Seq[ExperienceProductType] = Seq(
    `Structured/Market-Linked CDs`,
    `Structured/Market-Linked Principal Protected Notes`,
    `Structured/Market-Linked Principal At Risk Notes With European Barrier`,
    `Structured/Market-Linked Principal At Risk Notes With American Barrier or Sector Underlier`,
    `Structured/Market-Linked Growth Notes`,
    `Structured/Market-Linked Income Notes`
  )

  @EnumValues(
    "structured/Market-Linked CDs",
    "structured/Market-Linked Principal Protected Notes",
    "structured/Market-Linked Principal At Risk Notes With European Barrier",
    "structured/Market-Linked Principal At Risk Notes With American Barrier or Sector Underlier",
    "structured/Market-Linked Growth Notes",
    "structured/Market-Linked Income Notes"
  )
  case object Ref extends Reference
}

