package com.simonmarkets.unifiedportfolio.domain.data

import com.simonmarkets.entitlements.AcceptedAccessKeysGenerator
import com.simonmarkets.mongodb.codec.EnumEntryCodecProvider
import com.simonmarkets.resteasy.utils.SimonCodecs
import com.simonmarkets.unifiedportfolio.access.OwnerKeysGenerator
import com.simonmarkets.unifiedportfolio.domain.enums.lpl.{LPLAccountPrimaryClassification, LPLAccountSecondaryClassification, LPLAccountRType}
import com.simonmarkets.unifiedportfolio.domain.enums.{Currency, ExperienceProductType, HoldingType, LegalEntityType, OwnerType}
import com.simonmarkets.utils.data.mongo.model.SimonResourceCompanion
import io.simon.encryption.v2.model.EncryptedData
import org.bson.codecs.configuration.CodecProvider
import org.mongodb.scala.bson.codecs.Macros._

case class Owner(
    id: String,
    acceptedAccessKeys: Set[String], // necessary at top level for resteasy framework
    ownerType: OwnerType,
    name: EncryptedData,

    externalIds: Set[ExternalId],

    // these fields are going to be OwnerIds.
    // This reflects the infinitely nestable nature of Investment ownership.
    parentIds: Set[String],
    childIds: Set[String],

    auth: Authorization,
    timestamps: RecordTimestamps,

    details: OwnerDetails,
)

object Owner extends SimonResourceCompanion[Owner] {

  override def collectionName: String = "owners"

  override def resourceName: String = "Owner"

  override def accessKeyGenerator: AcceptedAccessKeysGenerator[Owner] = OwnerKeysGenerator

  override def codecProviders: List[CodecProvider] = List(
    createCodecProviderIgnoreNone[Owner],
    createCodecProviderIgnoreNone[EncryptedData],
    createCodecProviderIgnoreNone[Authorization],
    createCodecProviderIgnoreNone[RecordTimestamps],
    classOf[OwnerDetails],
    classOf[LegalEntity],
    createCodecProviderIgnoreNone[InvestorExperience],
    createCodecProviderIgnoreNone[Money],
    createCodecProviderIgnoreNone[ProductType],
    createCodecProviderIgnoreNone[LPLAccountExtensions],
    EnumEntryCodecProvider[ExperienceProductType],
    EnumEntryCodecProvider[Currency],
    EnumEntryCodecProvider[HoldingType],
    EnumEntryCodecProvider[LegalEntityType],
    EnumEntryCodecProvider[OwnerType],
    EnumEntryCodecProvider[LPLAccountPrimaryClassification],
    EnumEntryCodecProvider[LPLAccountSecondaryClassification],
    EnumEntryCodecProvider[LPLAccountRType],
    SimonCodecs.SimonIdProvider,
  )
}



