package com.simonmarkets.unifiedportfolio.repository

import com.simonmarkets.unifiedportfolio.domain.data.Owner
import com.simonmarkets.utils.data.core.GenericRepository
import com.simonmarkets.utils.data.mongo.SimonMongoRepository
import com.simonmarkets.utils.data.mongo.context.DatabaseContext
import org.mongodb.scala.bson.conversions.Bson
import org.mongodb.scala.model.{Filters, FindOneAndReplaceOptions}
import org.mongodb.scala.model.Filters.{equal, in}
import org.mongodb.scala.result.InsertManyResult

import scala.concurrent.{ExecutionContext, Future}

trait OwnerRepository extends GenericRepository[Owner] {
  def upsert(owner: Owner, availableAccessKeys: Set[String]): Future[Unit]
}

object OwnerRepository {

  class OwnerRepositoryMongo(implicit ec: ExecutionContext,
      dbCtx: DatabaseContext) extends SimonMongoRepository[Owner] with OwnerRepository {

    private lazy val collection = dbCtx.getCollection[Owner]("owners").withCodecRegistry(Owner.codecRegistry)

    def clearAllData: Future[Void] = {
      collection.drop().toFuture()
    }

    // Inserts the records without checking perms (in the context of an initial database seed
    // using example data).
    def seedRecords(owners: List[Owner]): Future[InsertManyResult] = {
      collection.insertMany(owners).toFuture()
    }

    override def upsert(owner: Owner, availableAccessKeys: Set[String]): Future[Unit] = {
      collection.findOneAndReplace(
        Filters.and(equal("id", owner.id), accessKeysCondition(availableAccessKeys)),
        owner,
        options = new FindOneAndReplaceOptions().upsert(true)
      ).completeWithUnit.toFuture
    }
  }

  private def accessKeysCondition(availableAccessKeys: Set[String]): Bson =
    in("acceptedAccessKeys", availableAccessKeys.toSeq: _*)

}

