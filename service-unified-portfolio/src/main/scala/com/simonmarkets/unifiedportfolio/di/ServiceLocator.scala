package com.simonmarkets.unifiedportfolio.di

import akka.actor.{Actor<PERSON><PERSON>, ActorSystem}
import akka.http.scaladsl.Http
import akka.stream.Materializer
import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.api.users.HttpACLClient
import com.simonmarkets.clients.{ContractOfferingsClient, HttpAddeparClient}
import com.simonmarkets.http.FutureHttpClient
import com.simonmarkets.logging.akkahttp.AkkaLoggingContextActor
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.unifiedportfolio.config.AppConfiguration
import com.simonmarkets.resteasy.authn.User
import com.simonmarkets.resteasy.framework.RestEasyCore
import com.simonmarkets.resteasy.requests.AuthorizedDirectives
import com.simonmarkets.syntax.futureOpsConversion
import com.simonmarkets.useracl.UserAclAuthorizedDirective
import com.simonmarkets.mongodb.Client
import com.simonmarkets.regbi.repository.{HoldingPresentationRepository, HoldingPresentationRepositoryMongo, OwnersPresentationRepository, OwnersPresentationRepositoryMongo}
import com.simonmarkets.regbi.service.{RegBiPresentationService, RegBiPresentationServiceImpl, SnowflakeProvider}
import com.simonmarkets.unifiedportfolio.repository.HoldingRepository.HoldingRepositoryMongo
import com.simonmarkets.unifiedportfolio.repository.OwnerRepository.OwnerRepositoryMongo
import com.simonmarkets.utils.data.mongo.context.DatabaseContext
import com.simonmarkets.unifiedportfolio.repository.{HoldingRepository, OwnerRepository}
import com.simonmarkets.unifiedportfolio.service.HoldingService.HoldingServiceImpl
import com.simonmarkets.unifiedportfolio.service.OwnerService.OwnerServiceImpl
import com.simonmarkets.unifiedportfolio.service.{AddeparServiceImpl, HoldingService, OwnerService}
import io.simon.encryption.v3.service.IcnEncryptionService

import scala.concurrent.ExecutionContext
import scala.concurrent.duration.Duration

/** Gathers up all service and repository dependencies */
class ServiceLocator(config: AppConfiguration)(
    implicit system: ActorSystem,
    materializer: Materializer,
    executionContext: ExecutionContext,
    startupTraceId: TraceId
) extends TraceLogging {
  log.info("Initializing service locator")
  val baseUrl = config.aclClientConfig.baseUrl
  val loggingContextActor: ActorRef = system.actorOf(AkkaLoggingContextActor.props())

  log.info("Initializing authorization directives")
  val futureHttpClient = new FutureHttpClient(Http(), config.aclClientConfig.httpClient)
  private val httpAclClient = HttpACLClient(config.aclClientConfig)

  private val systemUser = httpAclClient.getUserACL(config.systemUserId).await(Duration.Inf)
  private val userAclDirective = UserAclAuthorizedDirective(httpAclClient)
  val authDirective: AuthorizedDirectives[UserACL] = RestEasyCore.allowSystemCalls[UserACL](userAclDirective, User(systemUser.userId, "", None, Nil), systemUser)

  val encryptionService: IcnEncryptionService = new IcnEncryptionService(futureHttpClient, config.icnEncryption)

  val contractOfferingsClient: ContractOfferingsClient = ContractOfferingsClient(futureHttpClient, baseUrl)
  val addeparClient: HttpAddeparClient = HttpAddeparClient(futureHttpClient, config.addepar.baseUrl)
  val addeparService: AddeparServiceImpl = new AddeparServiceImpl(addeparClient)

  log.info("Starting mongo connection")
  private val mongoClient = Client.create(config.mongoDB.client)
  private val mongoDatabase = mongoClient.getDatabase(config.mongoDB.owner.database)
  private implicit val databaseContext: DatabaseContext = DatabaseContext(mongoDatabase, mongoClient)
  val ownerRepository: OwnerRepository = new OwnerRepositoryMongo()
  val ownerService: OwnerService = new OwnerServiceImpl(ownerRepository)
  val holdingRepository: HoldingRepository = new HoldingRepositoryMongo()
  val holdingService: HoldingService = new HoldingServiceImpl(holdingRepository)

  val ownersPresentationRepository: OwnersPresentationRepository = new OwnersPresentationRepositoryMongo()
  val holdingsPresentationRepository: HoldingPresentationRepository = new HoldingPresentationRepositoryMongo()

  val snowflakeProvider: SnowflakeProvider = SnowflakeProvider(config.snowflake)

  val regBiPresentationService: RegBiPresentationService = new RegBiPresentationServiceImpl(
    ownersPresentationRepository,
    holdingsPresentationRepository,
    encryptionService,
    contractOfferingsClient,
    snowflakeProvider
  )
}
