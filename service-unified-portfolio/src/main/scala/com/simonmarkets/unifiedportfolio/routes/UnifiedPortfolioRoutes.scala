package com.simonmarkets.unifiedportfolio.routes

import akka.http.scaladsl.server.Route
import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.regbi.api.request.GetHouseholdListRequest
import com.simonmarkets.regbi.domain.{HouseholdSortBy, SortOrder}
import com.simonmarkets.regbi.service.RegBiPresentationService
import com.simonmarkets.resteasy.DirectivesWithCirce
import com.simonmarkets.resteasy.requests.AuthorizedDirectives
import com.simonmarkets.unifiedportfolio.domain.enums.Partner.Addepar
import com.simonmarkets.unifiedportfolio.domain.integrations.addepar.models.{AddeparAccessToken, AddeparFirm}
import com.simonmarkets.unifiedportfolio.domain.requests.{DeleteOwnerRequest, GetHoldingRequest, GetOwnerRequest, OwnerListRequest, OwnersPatchRequest}
import com.simonmarkets.unifiedportfolio.service.{AddeparService, HoldingService, OwnerService}

class UnifiedPortfolioRoutes(
    authDirectives: AuthorizedDirectives[UserACL],
    ownerService: OwnerService,
    holdingService: HoldingService,
    regBiPresentationService: RegBiPresentationService,
    addeparService: AddeparService
) extends TraceLogging with DirectivesWithCirce with UpsCirceCodecs {

  import authDirectives._

  def routes: Route = {
    pathPrefix("simon" / "api" / "v1" / "unified-portfolio") {
      authorizedUser() { (dTraceId, _, dUserACL) =>
        implicit val traceId: TraceId = dTraceId
        implicit val userACL: UserACL = dUserACL
        concat(ownersRoutes, holdingsRoutes, presentationLayerRoutes, integrationRoutes)
      }
    }
  }

  private def ownersRoutes(implicit traceId: TraceId, userACL: UserACL): Route = {
    pathPrefix("owners") {
      // TODO add list route
      // TODO add possibility for multiple IDS
      path(Segment) { id =>
        get {
          rejectEmptyResponse {
            // TODO add idSource param
            complete(ownerService.get(GetOwnerRequest(id)))
          }
        } ~
          delete {
            complete(ownerService.delete(DeleteOwnerRequest(id)))
          } ~
          (patch & entity(as[OwnersPatchRequest])) { request =>
            complete(ownerService.ownerPatch(id, request))
          }
      }
    }
  }

  private def holdingsRoutes(implicit traceId: TraceId, userACL: UserACL): Route = {
    pathPrefix("holdings") {
      // TODO add list route
      // TODO add possibility for multiple IDS
      path(Segment) { id =>
        get {
          rejectEmptyResponse {
            // TODO add idSource param
            complete(holdingService.get(GetHoldingRequest(id, None)))
          }
        }
      }
    }
  }

  private def presentationLayerRoutes(implicit traceId: TraceId, userACL: UserACL): Route = {
    pathPrefix("presentation") {
      pathPrefix("snowflake-test") {
        get {
          complete(regBiPresentationService.getTestSnowflakeData)
        }
      } ~
      pathPrefix("regbi" / "households") {
        pathEnd {
          (get & parameters("filterByConcentration".as[String].repeated, "filterByLNW".as[String].repeated, "sortBy".as[HouseholdSortBy].?, "sortOrder".as[SortOrder].?, "pattern".?, "offset".as[Int].?, "limit".as[Int].?)) {
            (filterByConcentration, filterByLNW, sortBy, sortOrder, pattern, offset, limit) =>
            val byConcentration = if (filterByConcentration.nonEmpty) Some(filterByConcentration.toList) else None
            val byLNW = if (filterByLNW.nonEmpty) Some(filterByLNW.toList) else None
            val request = GetHouseholdListRequest(filterByConcentration = byConcentration, filterByLNW = byLNW, sortBy = sortBy, sortOrder = sortOrder, pattern = pattern, offset = offset, limit = limit)
            complete(regBiPresentationService.getHouseholdList(request))
          }
        } ~
          pathPrefix(Segment) { id =>
            path("issuer-concentration") {
              get {
                complete(regBiPresentationService.getHouseholdSiIssuerConcentration(id))
              }
            }
          }
      }
    }
  }

  private def integrationRoutes(implicit traceId: TraceId, userACL: UserACL): Route = {
    pathPrefix("integrations") {
      pathPrefix("addepar") {
        headerValueByName("x-addepar-token") { token =>
          headerValueByName("x-addepar-firm") { firm =>
            //These implicits are only temporary until we start storing them in mongo
            implicit val accessToken: AddeparAccessToken = AddeparAccessToken(token)
            implicit val addeparFirm: AddeparFirm = AddeparFirm(firm)

            pathPrefix("owners") {
              pathEnd {
                (get & parameters("offset".as[Int].?, "limit".as[Int].?)) { (offset, limit) =>
                  val request = OwnerListRequest(Some(Addepar), offset, limit)
                  complete(addeparService.getOwners(request))
                }
              } ~
                pathPrefix(Segment) { id =>
                  path("holdings") {
                    get {
                      val request = GetHoldingRequest(id, Some(Addepar))
                      complete(addeparService.getHoldings(request))
                    }
                  }
                }
            }
          }
        }
      }
    }
  }
}
