package com.simonmarkets.regbi.service

import akka.http.scaladsl.model.StatusCodes
import com.simonmarkets.clients.ContractOfferingsClient
import com.simonmarkets.clients.ContractOfferingsClient.{Offering, OfferingParams, OfferingsResult}
import com.simonmarkets.http.HttpError
import com.simonmarkets.logging.TraceId
import com.simonmarkets.regbi.TestData
import com.simonmarkets.regbi.api.request.GetHouseholdListRequest
import com.simonmarkets.regbi.domain.{HouseholdSortBy, Page, SortOrder}
import com.simonmarkets.regbi.repository.HoldingPresentationRepositoryMongo.ShallowHolding
import com.simonmarkets.regbi.repository.OwnersPresentationRepositoryMongo.{Account, HouseholdAggregation, HouseholdSummary, ItemCount, Profile, ShallowHousehold, ShallowHouseholdDetails}
import com.simonmarkets.regbi.repository.{HoldingPresentationRepository, OwnersPresentationRepository}
import com.simonmarkets.syntax._
import com.simonmarkets.unifiedportfolio.domain.data.{HouseholdDetails, LPLAccountExtensions, Money}
import com.simonmarkets.unifiedportfolio.domain.enums.Currency
import com.simonmarkets.unifiedportfolio.domain.enums.lpl.{LPLAccountPrimaryClassification, LPLAccountRType, LPLAccountSecondaryClassification}
import io.simon.encryption.v2.model.EncryptedData
import io.simon.encryption.v3.conversion.EncryptionReader
import io.simon.encryption.v3.service.IcnEncryptionService
import org.mockito.ArgumentMatchers.{any, eq => exact}
import org.mockito.Mockito.{never, reset, timeout, verify, when}
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{AsyncWordSpec, BeforeAndAfter, Matchers}

import java.util.concurrent.Executors

import scala.concurrent.ExecutionContext

class RegBiPresentationServiceSpec extends AsyncWordSpec with Matchers with BeforeAndAfter with MockitoSugar with TestData {

  private val ownersRepo = mock[OwnersPresentationRepository]
  private val holdingsRepo = mock[HoldingPresentationRepository]
  private val encryptionService = mock[IcnEncryptionService]
  private val contractOfferingsClient = mock[ContractOfferingsClient]
  private val snowflakeProvider: SnowflakeProvider = mock[SnowflakeProvider]

  private val serviceCtx = ExecutionContext.fromExecutor(Executors.newFixedThreadPool(1))

  private val regBiPresentationService: RegBiPresentationServiceImpl = new RegBiPresentationServiceImpl(ownersRepo, holdingsRepo, encryptionService, contractOfferingsClient, snowflakeProvider)(serviceCtx)

  before {
    reset(ownersRepo, holdingsRepo, encryptionService, contractOfferingsClient)
  }

  "getHouseholdList" should {
    val defaultRequest = GetHouseholdListRequest(filterByConcentration = None, filterByLNW = None, sortBy = None, sortOrder = None, pattern = None, offset = None, limit = None)
    val partnerExtensionsLPL = LPLAccountExtensions(primaryClassification = LPLAccountPrimaryClassification.SAM, secondaryClassification = LPLAccountSecondaryClassification.A1H, rType = LPLAccountRType.FS)
    val defaultAccount = Account(id = "accountId", name = EncryptedData("TEST ACCOUNT", "TEST ACCOUNT", Array[scala.Byte](), None), investmentObjective = "", documents = Seq.empty, totalValue = Some(Money(currency = Currency.USD, amount = 10000)), partnerExtensionsLPL = Some(partnerExtensionsLPL), None, None)
    val defaultProfile = Profile(id = "profileId", experience = 3, age = 33, name = EncryptedData("TEST PROFILE", "TEST PROFILE", Array[scala.Byte](), None), productTypes = List.empty, accounts = Seq(defaultAccount))
    val defaultHouseHoldSummary = HouseholdSummary(
      id = "id1",
      name = EncryptedData("TEST NAME", "TEST NAME", Array[scala.Byte](), None),
      lnw = 0,
      siConcentrationPercentage = 0,
      annualExpenses = None,
      availableLCF = None,
      obligations = None,
      profiles = Seq(defaultProfile)
    )
    "should return available households" in {
      when(ownersRepo.getHouseholdList(ids = Seq.empty,
        availableAccessKeys = Set("viewUnifiedOwnerViaNetwork:LPL"),
        sortOrder = SortOrder.Asc,
        sortBy = HouseholdSortBy.Id,
        filterByLNW = defaultRequest.filterByLNW,
        filterByConcentration = defaultRequest.filterByConcentration,
        offset = defaultRequest.offset,
        limit = defaultRequest.limit)).thenReturn(Seq.empty.successFuture)

      regBiPresentationService.getHouseholdList(defaultRequest).map { res =>
        verify(ownersRepo, never()).getHouseholdNames(any[Set[String]])(any[TraceId])
        verify(ownersRepo, timeout(3000).times(1)).getHouseholdList(ids = Seq.empty,
          availableAccessKeys = Set("viewUnifiedOwnerViaNetwork:LPL"),
          sortOrder = SortOrder.Asc,
          sortBy = HouseholdSortBy.Id,
          filterByLNW = defaultRequest.filterByLNW,
          filterByConcentration = defaultRequest.filterByConcentration,
          offset = defaultRequest.offset,
          limit = defaultRequest.limit)
        res shouldBe Page(totalCount = 0, result = List.empty)
      }
    }

    "should call get household names if pattern exists and apply pattern matching" in {
      val requestWithPattern = defaultRequest.copy(pattern = Some("test"), sortBy = Some(HouseholdSortBy.Name))
      val householdSummary1 = defaultHouseHoldSummary.copy(id = "id2", name = EncryptedData("NEW NAME", "NEW NAME", Array[scala.Byte](), None))
      val shallowHh1 = ShallowHousehold(id = defaultHouseHoldSummary.id, name = defaultHouseHoldSummary.name)
      val shallowHh2 = ShallowHousehold(id = householdSummary1.id, name = householdSummary1.name)

      when(ownersRepo.getHouseholdNames(Set("viewUnifiedOwnerViaNetwork:LPL"))).thenReturn(Seq(shallowHh1, shallowHh2).successFuture)
      when(encryptionService.bulkDecrypt(any[Map[String, EncryptedData]])(any[EncryptionReader[String]], any[TraceId])).thenReturn(
        Map(
          "name:0" -> defaultHouseHoldSummary.name.key,
          "name:1" -> householdSummary1.name.key,
          "profileName:0:0" -> defaultHouseHoldSummary.profiles.head.name.key,
          "accountName:0:0:0" -> defaultHouseHoldSummary.profiles.head.accounts.head.name.key,
        ).successFuture)
      when(ownersRepo.getHouseholdList(ids = Seq("id1"),
        availableAccessKeys = Set("viewUnifiedOwnerViaNetwork:LPL"),
        sortOrder = SortOrder.Asc,
        sortBy = requestWithPattern.sortBy.get,
        filterByLNW = requestWithPattern.filterByLNW,
        filterByConcentration = requestWithPattern.filterByConcentration,
        offset = requestWithPattern.offset,
        limit = requestWithPattern.limit)
      ).thenReturn(Seq(HouseholdAggregation(results = Seq(defaultHouseHoldSummary), totalCount = List(ItemCount(1)))).successFuture)

      regBiPresentationService.getHouseholdList(requestWithPattern).map { res =>
        verify(ownersRepo, timeout(3000).times(1)).getHouseholdNames(any[Set[String]])(any[TraceId])
        verify(ownersRepo, timeout(3000).times(1)).getHouseholdList(ids = exact(Seq("id1")),
          availableAccessKeys = exact(Set("viewUnifiedOwnerViaNetwork:LPL")),
          sortOrder = exact(SortOrder.Asc),
          sortBy = exact(requestWithPattern.sortBy.get),
          filterByLNW = exact(requestWithPattern.filterByLNW),
          filterByConcentration = exact(requestWithPattern.filterByConcentration),
          offset = exact(requestWithPattern.offset),
          limit = exact(requestWithPattern.limit))(exact(traceId))
        res.totalCount shouldBe 1
        res.result.size shouldBe 1
        res.result.head.name should startWith("TEST")
        res.result.head.profiles.head.name shouldBe "TEST PROFILE"
        res.result.head.profiles.head.accounts.head.name shouldBe "TEST ACCOUNT"
        res.result.head.profiles.head.accounts.head.partnerExtensionsLPL shouldBe Some(partnerExtensionsLPL)
      }
    }
  }

  "getHouseholdSiIssuerConcentration" should {
    "raise 404 if household not found" in {
      when(ownersRepo.getHouseholdById(any[String], any[Set[String]])(any[TraceId])).thenReturn(None.successFuture)
      recoverToExceptionIf[HttpError] {
        regBiPresentationService.getHouseholdSiIssuerConcentration("someId")
      }.map { ex =>
        ex.status shouldBe StatusCodes.NotFound
      }
    }

    "return household issuer concentration" in {
      val shallowHolding = ShallowHolding(id = defaultHolding.id, totalCommitment = defaultHolding.totalCommitment.get, productId = defaultHolding.productId)
      val offering = Offering(id = defaultHolding.productId.cusip.get, params = OfferingParams(issuerShortName = "issuerName"))
      val offeringsResult = OfferingsResult(results = List(offering))
      val shallowHouseholdDetails = ShallowHouseholdDetails(id = defaultHousehold1.id, details = defaultHousehold1.details.asInstanceOf[HouseholdDetails])
      when(ownersRepo.getHouseholdById(any[String], any[Set[String]])(any[TraceId])).thenReturn(Some(shallowHouseholdDetails).successFuture)
      when(holdingsRepo.getHoldingsByHousehold(defaultHousehold1.id, Set("viewUnifiedHoldingViaNetwork:LPL"))).thenReturn(Seq(shallowHolding).successFuture)
      when(contractOfferingsClient.getIssuerNames(Seq(shallowHolding.productId.cusip.get))).thenReturn(offeringsResult.successFuture)
      regBiPresentationService.getHouseholdSiIssuerConcentration(defaultHousehold1.id).map { res =>
        res.size shouldBe 1
        val issuerConcentration = res.head
        issuerConcentration.issuer shouldBe offering.params.issuerShortName
        issuerConcentration.amount shouldBe defaultHolding.totalCommitment.get.amount
        val expectedConcentration = 10000.toFloat / 100000 * 100
        issuerConcentration.concentration shouldBe expectedConcentration
      }
    }
  }
}
