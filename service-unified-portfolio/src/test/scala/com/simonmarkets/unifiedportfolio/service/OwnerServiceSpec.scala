package com.simonmarkets.unifiedportfolio.service

import akka.http.scaladsl.model.StatusCodes
import com.simonmarkets.http.HttpError
import com.simonmarkets.syntax._
import com.simonmarkets.unifiedportfolio.domain.data._
import com.simonmarkets.unifiedportfolio.domain.enums.Currency
import com.simonmarkets.unifiedportfolio.domain.requests.{GetOwnerRequest, HouseholdDetailsPatchFields, LegalEntityPersonPatchFields, OwnersPatchRequest}
import com.simonmarkets.unifiedportfolio.domain.view._
import com.simonmarkets.unifiedportfolio.repository.OwnerRepository
import com.simonmarkets.unifiedportfolio.service.OwnerService.OwnerServiceImpl
import com.simonmarkets.unifiedportfolio.utils.ExampleDataFactory
import org.mockito.ArgumentCaptor
import org.mockito.ArgumentMatchers.any
import org.mockito.Mockito.{reset, verify, when}
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{AsyncWordSpec, BeforeAndAfter, Matchers}

import java.util.concurrent.Executors

import scala.concurrent.ExecutionContext

class OwnerServiceSpec extends AsyncWordSpec with Matchers with BeforeAndAfter with MockitoSugar with ExampleDataFactory {

  private val ownersRepo = mock[OwnerRepository]
  private val serviceCtx = ExecutionContext.fromExecutor(Executors.newFixedThreadPool(1))
  private val ownerService: OwnerServiceImpl = new OwnerServiceImpl(ownersRepo)(serviceCtx)

  before {
    reset(ownersRepo)
  }

  "get" should {

    "when finding a Household" should {
      val household = exampleHousehold()
      val getHouseholdRequest = GetOwnerRequest(household.id)

      "should return a view of the household" in {
        when(ownersRepo.get(household.id, Set("viewUnifiedOwnerViaNetwork:SIMON Admin"))).thenReturn(Some(household).successFuture)

        val details: HouseholdDetails = household.details.asInstanceOf[HouseholdDetails]
        val expectedView = OwnerView(
          household.id,
          household.ownerType,
          Some("Decrypted Name"), // todo mock real decryption.
          household.parentIds,
          HouseholdDetailsView(
            details.liquidNetWorthMin,
            details.liquidNetWorthMax,
            details.liquidNetWorth,
            details.annualExpenses,
            details.availableLCF,
            details.obligations,
          ))

        ownerService.get(getHouseholdRequest).map { res =>
          res shouldBe Some(expectedView)
        }
      }
    }

    "when finding a LegalEntity" should {
      val person = examplePerson()
      val getPersonRequest = GetOwnerRequest(person.id)

      "should return a view of the person" in {
        when(ownersRepo.get(person.id, Set("viewUnifiedOwnerViaNetwork:SIMON Admin"))).thenReturn(Some(person).successFuture)

        val legalEntityDetails: LegalEntityDetails = person.details.asInstanceOf[LegalEntityDetails]
        val personEntity = legalEntityDetails.entity.asInstanceOf[Person]
        val expectedView = OwnerView(
          person.id,
          person.ownerType,
          Some("Decrypted Name"), // todo mock real decryption.
          person.parentIds,
          LegalEntityDetailsView(
            Some("DecryptedTaxId"),
            legalEntityDetails.entityType,
            PersonView(
              personEntity.experience,
              personEntity.age,
              personEntity.productTypes,
          )))
        ownerService.get(getPersonRequest).map { res =>
          res shouldBe Some(expectedView)
        }
      }
    }

    "when finding an Account" should {
      val account = exampleAccount()
      val getAccountRequest = GetOwnerRequest(account.id)

      "should return a view of the account" in {
        when(ownersRepo.get(account.id, Set("viewUnifiedOwnerViaNetwork:SIMON Admin"))).thenReturn(Some(account).successFuture)

        val accountDetails: AccountDetails = account.details.asInstanceOf[AccountDetails]
        val expectedView = OwnerView(
          account.id,
          account.ownerType,
          Some("Decrypted Name"), // todo mock real decryption.
          account.parentIds,
          AccountDetailsView(
            accountDetails.accountType,
            Some("DecryptedAccountNumber"),
            accountDetails.investmentObjective,
            accountDetails.totalValue,
            accountDetails.documents,
            accountDetails.partnerExtensionsLPL,
            None,
            None
          ))

        ownerService.get(getAccountRequest).map { res =>
          res shouldBe Some(expectedView)
        }
      }
    }
  }

  "ownerPatch" should {
    val defaultPatchRequest = OwnersPatchRequest(
      householdDetailsPatchFields = Some(
        HouseholdDetailsPatchFields(
          liquidNetWorth = SetTo(Money(amount = 100, currency = Currency.USD))
        )
      )
    )
    "raise http error for id with the wrong type" in {
      val account = exampleAccount()
      when(ownersRepo.get(account.id, Set("editUnifiedOwnerViaNetwork:SIMON Admin"))).thenReturn(Some(account).successFuture)
      recoverToExceptionIf[HttpError] {
        ownerService.ownerPatch(account.id, defaultPatchRequest)
      }.map { ex =>
        ex.status shouldBe StatusCodes.UnprocessableEntity
      }
    }

    "raise http error for bad request" in {
      val household = exampleHousehold()
      val wrongRequest = defaultPatchRequest.copy(legalEntityPersonPatchFields = Some(LegalEntityPersonPatchFields(productTypes = SetTo(Set.empty))))
      when(ownersRepo.get(household.id, Set("editUnifiedOwnerViaNetwork:SIMON Admin"))).thenReturn(Some(household).successFuture)
      recoverToExceptionIf[HttpError] {
        ownerService.ownerPatch(household.id, wrongRequest)
      }.map { ex =>
        ex.status shouldBe StatusCodes.UnprocessableEntity
      }
    }

    "update owner household" in {
      val household = exampleHousehold()
      when(ownersRepo.get(household.id, Set("editUnifiedOwnerViaNetwork:SIMON Admin"))).thenReturn(Some(household).successFuture)
      when(ownersRepo.upsert(any[Owner], any[Set[String]])).thenReturn(().successFuture)

      ownerService.ownerPatch(household.id, defaultPatchRequest).map { _ =>
        val upserted: ArgumentCaptor[Owner] = ArgumentCaptor.forClass(classOf[Owner])
        verify(ownersRepo).upsert(upserted.capture, any[Set[String]])
        val updatedOwner = upserted.getValue
        val updatedHouseholdDetails = updatedOwner.details.asInstanceOf[HouseholdDetails]
        val initialHouseholdDetails = household.details.asInstanceOf[HouseholdDetails]

        initialHouseholdDetails.liquidNetWorth should not be updatedHouseholdDetails.liquidNetWorth
        val expectedLNW = defaultPatchRequest.householdDetailsPatchFields.get.liquidNetWorth match {
          case SetTo(value) => Some(value)
          case _ => None
        }
        updatedHouseholdDetails.liquidNetWorth shouldBe expectedLNW
        updatedHouseholdDetails.obligations shouldBe initialHouseholdDetails.obligations
        updatedHouseholdDetails.availableLCF shouldBe initialHouseholdDetails.availableLCF
        updatedHouseholdDetails.annualExpenses shouldBe initialHouseholdDetails.annualExpenses
      }
    }

      "update owner legal entity" in {
        val person = examplePerson()
        val legalEntityPatchRequest = OwnersPatchRequest(legalEntityPersonPatchFields = Some(LegalEntityPersonPatchFields(productTypes = Clear)))
        when(ownersRepo.get(person.id, Set("editUnifiedOwnerViaNetwork:SIMON Admin"))).thenReturn(Some(person).successFuture)
        when(ownersRepo.upsert(any[Owner], any[Set[String]])).thenReturn(().successFuture)

        ownerService.ownerPatch(person.id, legalEntityPatchRequest).map { _ =>
          val upserted: ArgumentCaptor[Owner] = ArgumentCaptor.forClass(classOf[Owner])
          verify(ownersRepo).upsert(upserted.capture, any[Set[String]])
          val updatedOwner = upserted.getValue
          val updatedPersonDetails = updatedOwner.details.asInstanceOf[LegalEntityDetails].entity.asInstanceOf[Person]
          val initialPersonDetails = person.details.asInstanceOf[LegalEntityDetails].entity.asInstanceOf[Person]

          initialPersonDetails.productTypes.size should not be updatedPersonDetails.productTypes.size
          updatedPersonDetails.productTypes shouldBe Set[ProductType]()
        }
    }
  }
  // TODO add  "delete" unit tests, only after two things happen:
  // 1. We've imported our Capabilities from Networks repo, which would include EditOwnerViaNetwork
  // 2. We've implemented the adjustment to other entities' ownership structure in response to deleting
  // an Owner
}
