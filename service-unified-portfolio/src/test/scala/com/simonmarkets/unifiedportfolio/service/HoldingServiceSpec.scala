package com.simonmarkets.unifiedportfolio.service

import com.simonmarkets.syntax._
import com.simonmarkets.unifiedportfolio.domain.data.{AlternativeDetails, AnnuityDetails, TraditionalDetails}
import com.simonmarkets.unifiedportfolio.domain.requests.GetHoldingRequest
import com.simonmarkets.unifiedportfolio.domain.view._
import com.simonmarkets.unifiedportfolio.repository.HoldingRepository
import com.simonmarkets.unifiedportfolio.service.HoldingService.HoldingServiceImpl
import com.simonmarkets.unifiedportfolio.utils.ExampleDataFactory
import org.mockito.Mockito.{reset, when}
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{AsyncWordSpec, BeforeAndAfter, Matchers}

import java.util.concurrent.Executors

import scala.concurrent.ExecutionContext

class HoldingServiceSpec extends AsyncWordSpec with Matchers with BeforeAndAfter with MockitoSugar with ExampleDataFactory {

  private val holdingsRepo = mock[HoldingRepository]
  private val serviceCtx = ExecutionContext.fromExecutor(Executors.newFixedThreadPool(1))
  private val holdingService: HoldingServiceImpl = new HoldingServiceImpl(holdingsRepo)(serviceCtx)

  before {
    reset(holdingsRepo)
  }

  "get" should {

    "when finding a Structured Investment" should {
      val household = exampleHousehold()
      val person = examplePerson(household)
      val account = exampleAccount(person)
      val holding = exampleSiHolding(account, 1, Set(household.id, person.id, account.id))
      val getHoldingRequest = GetHoldingRequest(holding.id, None)

      "should return a view of the holding" in {
        when(holdingsRepo.get(holding.id, Set("viewUnifiedHoldingViaNetwork:SIMON Admin"))).thenReturn(Some(holding).successFuture)

        val expectedView = HoldingView(
          holding.id,
          holding.holdingType,
          holding.productId,
          holding.marketValue,
          holding.totalCommitment,
          holding.withdrawableAmount,
          holding.ownerships.map(o => OwnershipView(o.ownerId, Some("Decrypted Name"), o.ownerType, o.stake)),
          StructuredInvestmentDetailsView()
        )

        holdingService.get(getHoldingRequest).map { res =>
          res shouldBe Some(expectedView)
        }
      }
    }

    "when finding an Alternative Investment" should {
      val household = exampleHousehold()
      val person = examplePerson(household)
      val account = exampleAccount(person)
      val holding = exampleAltsHolding(account, 1, Set(household.id, person.id, account.id))
      val deets = holding.details.asInstanceOf[AlternativeDetails]
      val getHoldingRequest = GetHoldingRequest(holding.id, None)

      "should return a view of the holding" in {
        when(holdingsRepo.get(holding.id, Set("viewUnifiedHoldingViaNetwork:SIMON Admin"))).thenReturn(Some(holding).successFuture)

        val expectedView = HoldingView(
          holding.id,
          holding.holdingType,
          holding.productId,
          holding.marketValue,
          holding.totalCommitment,
          holding.withdrawableAmount,
          holding.ownerships.map(o => OwnershipView(o.ownerId, Some("Decrypted Name"), o.ownerType, o.stake)),
          AlternativeDetailsView(deets.shareClassId)
        )

        holdingService.get(getHoldingRequest).map { res =>
          res shouldBe Some(expectedView)
        }
      }
    }

    "when finding an Annuity" should {
      val household = exampleHousehold()
      val person = examplePerson(household)
      val account = exampleAccount(person)
      val holding = exampleAnnuitiesHolding(account, 1, Set(household.id, person.id, account.id))
      val deets = holding.details.asInstanceOf[AnnuityDetails]
      val getHoldingRequest = GetHoldingRequest(holding.id, None)

      "should return a view of the holding" in {
        when(holdingsRepo.get(holding.id, Set("viewUnifiedHoldingViaNetwork:SIMON Admin"))).thenReturn(Some(holding).successFuture)

        val expectedView = HoldingView(
          holding.id,
          holding.holdingType,
          holding.productId,
          holding.marketValue,
          holding.totalCommitment,
          holding.withdrawableAmount,
          holding.ownerships.map(o => OwnershipView(o.ownerId, Some("Decrypted Name"), o.ownerType, o.stake)),
          AnnuityDetailsView(
            deets.totalContractAmount,
            deets.surrenderValue,
            deets.totalPremium,
            deets.policyNumber,
            deets.productName,
            deets.carrierKey,
            deets.annuityAssetClass)
        )

        holdingService.get(getHoldingRequest).map { res =>
          res shouldBe Some(expectedView)
        }
      }
    }

    "when finding a Traditonal Investment" should {
      val household = exampleHousehold()
      val person = examplePerson(household)
      val account = exampleAccount(person)
      val holding = exampleTraditionalHolding(account, 1, Set(household.id, person.id, account.id))
      val deets = holding.details.asInstanceOf[TraditionalDetails]
      val getHoldingRequest = GetHoldingRequest(holding.id, None)

      "should return a view of the holding" in {
        when(holdingsRepo.get(holding.id, Set("viewUnifiedHoldingViaNetwork:SIMON Admin"))).thenReturn(Some(holding).successFuture)

        val expectedView = HoldingView(
          holding.id,
          holding.holdingType,
          holding.productId,
          holding.marketValue,
          holding.totalCommitment,
          holding.withdrawableAmount,
          holding.ownerships.map(o => OwnershipView(o.ownerId, Some("Decrypted Name"), o.ownerType, o.stake)),
          TraditionalDetailsView(deets.ticker))

        holdingService.get(getHoldingRequest).map { res =>
          res shouldBe Some(expectedView)
        }
      }
    }
  }
}
