package com.simonmarkets.unifiedportfolio.clients

import akka.actor.ActorSystem
import akka.http.scaladsl.model.{HttpHeader, StatusCodes, Uri}
import akka.stream.Materializer
import com.simonmarkets.clients.HttpAddeparClient
import com.simonmarkets.http.FutureHttpClient.RequestTag
import com.simonmarkets.http.retry.RetryStrategy
import com.simonmarkets.http.{FutureHttpClient, HttpDecoder, HttpEncoder, HttpError}
import com.simonmarkets.logging.TraceId
import com.simonmarkets.syntax.anyOpsConversion
import com.simonmarkets.unifiedportfolio.domain.enums.Partner.Addepar
import com.simonmarkets.unifiedportfolio.domain.integrations.addepar.requests.PortfolioQueryRequest
import com.simonmarkets.unifiedportfolio.domain.integrations.addepar.responses.{AddeparEntitiesResponse, PortfolioQueryResponse}
import com.simonmarkets.unifiedportfolio.domain.integrations.addepar.utils.AddeparSampleDataFactory
import com.simonmarkets.unifiedportfolio.domain.requests.{GetHoldingRequest, OwnerListRequest}
import com.simonmarkets.unifiedportfolio.utils.ExampleDataFactory
import org.mockito.ArgumentMatchers.any
import org.mockito.Mockito.when
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{AsyncWordSpec, Matchers}

class HttpAddeparClientSpec extends AsyncWordSpec with Matchers with MockitoSugar with AddeparSampleDataFactory with ExampleDataFactory{
  private val mockHttpClient = mock[FutureHttpClient]
  private implicit lazy val actorSystem: ActorSystem = ActorSystem("AddeparClientTest")
  private implicit lazy val mat: Materializer = Materializer(actorSystem)
  private val addeparHttpClient = HttpAddeparClient(mockHttpClient, "")

  "getEntitiesByType" should {
    val request = OwnerListRequest(Some(Addepar), None, None)

    "return 400 with custom error when Addepar returns 401" in {
      when(mockHttpClient.get[AddeparEntitiesResponse](any[Uri], any[List[HttpHeader]])(any[HttpDecoder[AddeparEntitiesResponse]], any[TraceId], any[RetryStrategy], any[RequestTag]))
        .thenReturn(HttpError.unauthorized("not authorized").failedFuture)

      recoverToExceptionIf[HttpError] {
        addeparHttpClient.getEntitiesByType(request)
      }.map { ex =>
        ex.status shouldBe StatusCodes.BadRequest
        ex.getMessage.contains("Addepar OAuth Error") shouldBe true
      }
    }

    "return 400 with custom error when Addepar returns 400" in {
      when(mockHttpClient.get[AddeparEntitiesResponse](any[Uri], any[List[HttpHeader]])(any[HttpDecoder[AddeparEntitiesResponse]], any[TraceId], any[RetryStrategy], any[RequestTag]))
        .thenReturn(HttpError.badRequest("bad request").failedFuture)

      recoverToExceptionIf[HttpError] {
        addeparHttpClient.getEntitiesByType(request)
      }.map { ex =>
        ex.status shouldBe StatusCodes.BadRequest
        ex.getMessage.contains("Unable to retrieve results") shouldBe true
      }
    }

    "return 404 with custom error when Addepar returns 404" in {
      when(mockHttpClient.get[AddeparEntitiesResponse](any[Uri], any[List[HttpHeader]])(any[HttpDecoder[AddeparEntitiesResponse]], any[TraceId], any[RetryStrategy], any[RequestTag]))
        .thenReturn(HttpError.notFound("not found").failedFuture)

      recoverToExceptionIf[HttpError] {
        addeparHttpClient.getEntitiesByType(request)
      }.map { ex =>
        ex.status shouldBe StatusCodes.NotFound
        ex.getMessage.contains("Addepar data not found") shouldBe true
      }
    }

    "return 500 with custom error when Addepar returns any other error type" in {
      when(mockHttpClient.get[AddeparEntitiesResponse](any[Uri], any[List[HttpHeader]])(any[HttpDecoder[AddeparEntitiesResponse]], any[TraceId], any[RetryStrategy], any[RequestTag]))
        .thenReturn(HttpError.internalServerError("random error").failedFuture)

      recoverToExceptionIf[HttpError] {
        addeparHttpClient.getEntitiesByType(request)
      }.map { ex =>
        ex.status shouldBe StatusCodes.InternalServerError
        ex.getMessage.contains("Addepar API Error") shouldBe true
      }
    }
  }

  "portfolioQueryById" should {
    val request = GetHoldingRequest("123", Some(Addepar))

    "return 400 with custom error when Addepar returns 401" in {
      when(mockHttpClient.post[PortfolioQueryRequest, PortfolioQueryResponse](any[Uri], any[PortfolioQueryRequest], any[List[HttpHeader]])(any[HttpEncoder[PortfolioQueryRequest]],any[HttpDecoder[PortfolioQueryResponse]], any[TraceId], any[RetryStrategy], any[RequestTag]))
        .thenReturn(HttpError.unauthorized("not authorized").failedFuture)

      recoverToExceptionIf[HttpError] {
        addeparHttpClient.portfolioQueryById(request)
      }.map { ex =>
        ex.status shouldBe StatusCodes.BadRequest
        ex.getMessage.contains("Addepar OAuth Error") shouldBe true
      }
    }

    "return 400 with custom error when Addepar returns 400" in {
      when(mockHttpClient.post[PortfolioQueryRequest, PortfolioQueryResponse](any[Uri], any[PortfolioQueryRequest], any[List[HttpHeader]])(any[HttpEncoder[PortfolioQueryRequest]],any[HttpDecoder[PortfolioQueryResponse]], any[TraceId], any[RetryStrategy], any[RequestTag]))
        .thenReturn(HttpError.badRequest("bad request").failedFuture)

      recoverToExceptionIf[HttpError] {
        addeparHttpClient.portfolioQueryById(request)
      }.map { ex =>
        ex.status shouldBe StatusCodes.BadRequest
        ex.getMessage.contains("Unable to retrieve results") shouldBe true
      }
    }

    "return 404 with custom error when Addepar returns 404" in {
      when(mockHttpClient.post[PortfolioQueryRequest, PortfolioQueryResponse](any[Uri], any[PortfolioQueryRequest], any[List[HttpHeader]])(any[HttpEncoder[PortfolioQueryRequest]],any[HttpDecoder[PortfolioQueryResponse]], any[TraceId], any[RetryStrategy], any[RequestTag]))
        .thenReturn(HttpError.notFound("not found").failedFuture)

      recoverToExceptionIf[HttpError] {
        addeparHttpClient.portfolioQueryById(request)
      }.map { ex =>
        ex.status shouldBe StatusCodes.NotFound
        ex.getMessage.contains("Addepar data not found") shouldBe true
      }
    }

    "return 500 with custom error when Addepar returns any other error type" in {
      when(mockHttpClient.post[PortfolioQueryRequest, PortfolioQueryResponse](any[Uri], any[PortfolioQueryRequest], any[List[HttpHeader]])(any[HttpEncoder[PortfolioQueryRequest]],any[HttpDecoder[PortfolioQueryResponse]], any[TraceId], any[RetryStrategy], any[RequestTag]))
        .thenReturn(HttpError.internalServerError("random error").failedFuture)

      recoverToExceptionIf[HttpError] {
        addeparHttpClient.portfolioQueryById(request)
      }.map { ex =>
        ex.status shouldBe StatusCodes.InternalServerError
        ex.getMessage.contains("Addepar API Error") shouldBe true
      }
    }
  }
}
